# 代码修复Agent

基于LangGraph的代码质量Issue自动修复Agent，支持智能分析和修复Java代码中的质量问题。

## 🚀 特性

- **智能修复**: 基于GPT-4的智能代码分析和修复
- **LangGraph工作流**: 使用最新的LangGraph框架构建Agent工作流
- **完整日志系统**: 详细的修复过程记录和性能统计
- **批量处理**: 支持批量处理多个文件
- **规则支持**: 支持多种代码质量规则（FindBugs、SonarQube等）
- **反思验证**: 内置修复方案验证机制

## 📦 安装

1. 克隆项目：
```bash
git clone <repository-url>
cd autofix
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 配置环境变量：
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的 OpenAI API Key
```

## 🔧 使用方法

### 基本使用

```bash
# 处理所有文件
python -m src.code_fix_agent.cli

# 处理单个文件
python -m src.code_fix_agent.cli "src/main/java/Example.java"

# 查看日志分析
python -m src.code_fix_agent.cli --logs
```

### 高级选项

```bash
# 指定issues文件
python -m src.code_fix_agent.cli --issues-file custom_issues.json

# 设置日志级别
LOG_LEVEL=DEBUG python -m src.code_fix_agent.cli
```

## 📊 日志系统

项目包含完整的日志记录系统，支持：

- **修复过程跟踪**: 详细记录每个修复步骤
- **性能统计**: 处理时间、修复速度等指标
- **错误分析**: 完整的错误记录和分类
- **日志分析**: 自动生成统计报告

### 日志文件

```
logs/
├── agent.log              # 主要应用日志
├── autofix_argument.log   # 修复操作记录
├── fix_stats.log          # 性能统计
└── errors.log             # 错误记录
```

### 查看日志分析

```bash
# 命令行查看
python -m src.code_fix_agent.cli --logs

# 或直接运行分析器
python src/code_fix_agent/log_analyzer.py
```

## 🛠️ 配置

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `OPENAI_API_KEY` | OpenAI API密钥 | 必需 |
| `OPENAI_BASE_URL` | API基础URL | `https://api.openai.com/v1` |
| `LOG_LEVEL` | 日志级别 | `INFO` |
| `LOG_DIR` | 日志目录 | `logs` |

### 日志级别

- `DEBUG`: 显示所有调试信息
- `INFO`: 显示一般信息（默认）
- `WARNING`: 只显示警告和错误
- `ERROR`: 只显示错误信息
- `CRITICAL`: 只显示严重错误

## 📈 性能监控

使用内置的日志分析工具监控修复效果：

```python
from src.code_fix_agent.log_analyzer import LogAnalyzer

analyzer = LogAnalyzer()
summary = analyzer.get_fix_summary()
print(f"修复成功率: {summary['success_rate']:.2%}")
```

## 🧪 测试

运行测试验证功能：

```bash
# 测试日志系统
python test_logging.py

# 测试修复功能（需要配置API密钥）
python -m src.code_fix_agent.cli "test_file.java"
```

## 📚 文档

- [日志系统详细说明](docs/LOGGING.md)
- [项目总结](PROJECT_SUMMARY.md)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 🔗 相关链接

- [LangGraph文档](https://langchain-ai.github.io/langgraph/)
- [OpenAI API文档](https://platform.openai.com/docs)

---

**注意**: 使用前请确保已正确配置OpenAI API密钥，并且有足够的API配额。
