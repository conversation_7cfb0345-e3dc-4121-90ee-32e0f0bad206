"""
代码修复Agent的日志记录模块
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path


class CodeFixLogger:
    """代码修复专用日志记录器"""
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        """
        初始化日志记录器
        
        Args:
            log_dir: 日志文件目录
            log_level: 日志级别
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志级别
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # 创建日志记录器
        self.logger = logging.getLogger("code_fix_agent")
        self.logger.setLevel(self.log_level)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
        
        # 修复操作日志文件
        self.fix_log_file = self.log_dir / "autofix_argument.log"
        
        # 性能统计日志文件
        self.stats_log_file = self.log_dir / "fix_stats.log"
        
        # 错误日志文件
        self.error_log_file = self.log_dir / "errors.log"
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            self.log_dir / "agent.log", 
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def log_fix_start(self, file_path: str, issues: List[Dict[str, Any]]):
        """记录修复开始"""
        self.logger.info(f"🚀 开始修复文件: {file_path}")
        self.logger.info(f"📋 发现 {len(issues)} 个问题")
        
        # 详细记录问题信息
        for i, issue in enumerate(issues, 1):
            rule = issue.get("rule", "Unknown")
            line = issue.get("line", "Unknown")
            severity = issue.get("severity", "Unknown")
            self.logger.debug(f"  问题 {i}: {rule} (行 {line}, 严重性: {severity})")
    
    def log_rule_query(self, rule_key: str, success: bool, details: Optional[str] = None):
        """记录规则查询"""
        if success:
            self.logger.debug(f"✅ 成功获取规则详情: {rule_key}")
        else:
            self.logger.warning(f"⚠️ 规则查询失败: {rule_key}")
            if details:
                self.logger.warning(f"   详情: {details}")
    
    def log_fix_generation(self, file_path: str, fix_count: int, success: bool, error: Optional[str] = None):
        """记录修复方案生成"""
        if success:
            self.logger.info(f"✅ 生成了 {fix_count} 个修复建议 for {file_path}")
        else:
            self.logger.error(f"❌ 修复方案生成失败 for {file_path}")
            if error:
                self.logger.error(f"   错误: {error}")
    
    def log_fix_application(self, file_path: str, applied_fixes: int, total_fixes: int, 
                          fix_suggestions: List[Dict[str, Any]]):
        """记录修复应用结果"""
        self.logger.info(f"📝 修复应用完成: {applied_fixes}/{total_fixes} for {file_path}")
        
        # 记录到修复日志文件
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "file_path": file_path,
            "applied_fixes": applied_fixes,
            "total_fixes": total_fixes,
            "success_rate": applied_fixes / total_fixes if total_fixes > 0 else 0,
            "fixes": fix_suggestions
        }
        
        self._write_json_log(self.fix_log_file, log_entry)
        
        # 记录详细的修复信息
        for i, fix in enumerate(fix_suggestions, 1):
            line = fix.get("line", "Unknown")
            operation = fix.get("operation", "replace")
            self.logger.debug(f"  修复 {i}: 行 {line} ({operation})")
    
    def log_error(self, error_type: str, error_message: str, file_path: Optional[str] = None, 
                  context: Optional[Dict[str, Any]] = None):
        """记录错误信息"""
        self.logger.error(f"❌ {error_type}: {error_message}")
        
        # 记录到错误日志文件
        error_entry = {
            "timestamp": datetime.now().isoformat(),
            "error_type": error_type,
            "error_message": error_message,
            "file_path": file_path,
            "context": context or {}
        }
        
        self._write_json_log(self.error_log_file, error_entry)
    
    def log_performance_stats(self, file_path: str, processing_time: float, 
                            issues_count: int, fixes_applied: int):
        """记录性能统计"""
        stats_entry = {
            "timestamp": datetime.now().isoformat(),
            "file_path": file_path,
            "processing_time_seconds": processing_time,
            "issues_count": issues_count,
            "fixes_applied": fixes_applied,
            "fixes_per_second": fixes_applied / processing_time if processing_time > 0 else 0
        }
        
        self._write_json_log(self.stats_log_file, stats_entry)
        
        self.logger.info(f"⏱️ 处理时间: {processing_time:.2f}s, 修复速度: {fixes_applied/processing_time:.2f} fixes/s")
    
    def _write_json_log(self, log_file: Path, log_entry: Dict[str, Any]):
        """写入JSON格式的日志"""
        try:
            with open(log_file, "a", encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
        except Exception as e:
            self.logger.error(f"写入日志文件失败 {log_file}: {e}")
    
    def get_fix_summary(self) -> Dict[str, Any]:
        """获取修复统计摘要"""
        try:
            if not self.fix_log_file.exists():
                return {"total_files": 0, "total_fixes": 0, "success_rate": 0}
            
            total_files = 0
            total_fixes = 0
            applied_fixes = 0
            
            with open(self.fix_log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        total_files += 1
                        total_fixes += entry.get("total_fixes", 0)
                        applied_fixes += entry.get("applied_fixes", 0)
                    except json.JSONDecodeError:
                        continue
            
            success_rate = applied_fixes / total_fixes if total_fixes > 0 else 0
            
            return {
                "total_files": total_files,
                "total_fixes": total_fixes,
                "applied_fixes": applied_fixes,
                "success_rate": success_rate
            }
            
        except Exception as e:
            self.logger.error(f"获取修复统计失败: {e}")
            return {"error": str(e)}


# 全局日志记录器实例
_logger_instance = None

def get_logger() -> CodeFixLogger:
    """获取全局日志记录器实例"""
    global _logger_instance
    if _logger_instance is None:
        log_level = os.getenv("LOG_LEVEL", "INFO")
        _logger_instance = CodeFixLogger(log_level=log_level)
    return _logger_instance


def log_fix_start(file_path: str, issues: List[Dict[str, Any]]):
    """便捷函数：记录修复开始"""
    get_logger().log_fix_start(file_path, issues)


def log_fix_application(file_path: str, applied_fixes: int, total_fixes: int, 
                       fix_suggestions: List[Dict[str, Any]]):
    """便捷函数：记录修复应用"""
    get_logger().log_fix_application(file_path, applied_fixes, total_fixes, fix_suggestions)


def log_error(error_type: str, error_message: str, file_path: Optional[str] = None, 
              context: Optional[Dict[str, Any]] = None):
    """便捷函数：记录错误"""
    get_logger().log_error(error_type, error_message, file_path, context)


def log_performance_stats(file_path: str, processing_time: float, 
                         issues_count: int, fixes_applied: int):
    """便捷函数：记录性能统计"""
    get_logger().log_performance_stats(file_path, processing_time, issues_count, fixes_applied)
