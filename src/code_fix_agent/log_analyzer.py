"""
日志分析工具
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import statistics


class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_dir: str = "logs"):
        """
        初始化日志分析器
        
        Args:
            log_dir: 日志文件目录
        """
        self.log_dir = Path(log_dir)
        self.fix_log_file = self.log_dir / "autofix_argument.log"
        self.stats_log_file = self.log_dir / "fix_stats.log"
        self.error_log_file = self.log_dir / "errors.log"
        self.agent_log_file = self.log_dir / "agent.log"
    
    def get_fix_summary(self) -> Dict[str, Any]:
        """获取修复统计摘要"""
        if not self.fix_log_file.exists():
            return {
                "total_files": 0,
                "total_fixes": 0,
                "applied_fixes": 0,
                "success_rate": 0,
                "files": []
            }
        
        total_files = 0
        total_fixes = 0
        applied_fixes = 0
        files_data = []
        
        try:
            with open(self.fix_log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        total_files += 1
                        file_total = entry.get("total_fixes", 0)
                        file_applied = entry.get("applied_fixes", 0)
                        total_fixes += file_total
                        applied_fixes += file_applied
                        
                        files_data.append({
                            "file_path": entry.get("file_path", "Unknown"),
                            "timestamp": entry.get("timestamp", "Unknown"),
                            "total_fixes": file_total,
                            "applied_fixes": file_applied,
                            "success_rate": file_applied / file_total if file_total > 0 else 0
                        })
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            return {"error": f"读取修复日志失败: {e}"}
        
        success_rate = applied_fixes / total_fixes if total_fixes > 0 else 0
        
        return {
            "total_files": total_files,
            "total_fixes": total_fixes,
            "applied_fixes": applied_fixes,
            "success_rate": success_rate,
            "files": files_data
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.stats_log_file.exists():
            return {
                "total_processing_time": 0,
                "average_processing_time": 0,
                "total_files_processed": 0,
                "average_fixes_per_second": 0,
                "files": []
            }
        
        processing_times = []
        fixes_per_second_list = []
        files_data = []
        
        try:
            with open(self.stats_log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        processing_time = entry.get("processing_time_seconds", 0)
                        fixes_per_second = entry.get("fixes_per_second", 0)
                        
                        processing_times.append(processing_time)
                        if fixes_per_second > 0:
                            fixes_per_second_list.append(fixes_per_second)
                        
                        files_data.append({
                            "file_path": entry.get("file_path", "Unknown"),
                            "timestamp": entry.get("timestamp", "Unknown"),
                            "processing_time": processing_time,
                            "issues_count": entry.get("issues_count", 0),
                            "fixes_applied": entry.get("fixes_applied", 0),
                            "fixes_per_second": fixes_per_second
                        })
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            return {"error": f"读取性能日志失败: {e}"}
        
        return {
            "total_processing_time": sum(processing_times),
            "average_processing_time": statistics.mean(processing_times) if processing_times else 0,
            "median_processing_time": statistics.median(processing_times) if processing_times else 0,
            "total_files_processed": len(processing_times),
            "average_fixes_per_second": statistics.mean(fixes_per_second_list) if fixes_per_second_list else 0,
            "files": files_data
        }
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误统计"""
        if not self.error_log_file.exists():
            return {
                "total_errors": 0,
                "error_types": {},
                "recent_errors": []
            }
        
        total_errors = 0
        error_types = {}
        all_errors = []
        
        try:
            with open(self.error_log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        total_errors += 1
                        
                        error_type = entry.get("error_type", "Unknown")
                        error_types[error_type] = error_types.get(error_type, 0) + 1
                        
                        all_errors.append({
                            "timestamp": entry.get("timestamp", "Unknown"),
                            "error_type": error_type,
                            "error_message": entry.get("error_message", ""),
                            "file_path": entry.get("file_path", ""),
                            "context": entry.get("context", {})
                        })
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            return {"error": f"读取错误日志失败: {e}"}
        
        # 获取最近的10个错误
        recent_errors = sorted(all_errors, key=lambda x: x["timestamp"], reverse=True)[:10]
        
        return {
            "total_errors": total_errors,
            "error_types": error_types,
            "recent_errors": recent_errors,
            "all_errors": all_errors
        }
    
    def get_daily_stats(self, days: int = 7) -> Dict[str, Any]:
        """获取每日统计（最近N天）"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        daily_stats = {}
        
        # 初始化每日统计
        for i in range(days):
            date = (start_date + timedelta(days=i)).strftime("%Y-%m-%d")
            daily_stats[date] = {
                "files_processed": 0,
                "fixes_applied": 0,
                "errors": 0,
                "processing_time": 0
            }
        
        # 统计修复数据
        if self.fix_log_file.exists():
            try:
                with open(self.fix_log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            entry = json.loads(line.strip())
                            timestamp = entry.get("timestamp", "")
                            if timestamp:
                                date = timestamp.split("T")[0]
                                if date in daily_stats:
                                    daily_stats[date]["files_processed"] += 1
                                    daily_stats[date]["fixes_applied"] += entry.get("applied_fixes", 0)
                        except (json.JSONDecodeError, ValueError):
                            continue
            except Exception:
                pass
        
        # 统计性能数据
        if self.stats_log_file.exists():
            try:
                with open(self.stats_log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            entry = json.loads(line.strip())
                            timestamp = entry.get("timestamp", "")
                            if timestamp:
                                date = timestamp.split("T")[0]
                                if date in daily_stats:
                                    daily_stats[date]["processing_time"] += entry.get("processing_time_seconds", 0)
                        except (json.JSONDecodeError, ValueError):
                            continue
            except Exception:
                pass
        
        # 统计错误数据
        if self.error_log_file.exists():
            try:
                with open(self.error_log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            entry = json.loads(line.strip())
                            timestamp = entry.get("timestamp", "")
                            if timestamp:
                                date = timestamp.split("T")[0]
                                if date in daily_stats:
                                    daily_stats[date]["errors"] += 1
                        except (json.JSONDecodeError, ValueError):
                            continue
            except Exception:
                pass
        
        return daily_stats
    
    def generate_report(self) -> str:
        """生成完整的分析报告"""
        fix_summary = self.get_fix_summary()
        performance_stats = self.get_performance_stats()
        error_summary = self.get_error_summary()
        daily_stats = self.get_daily_stats()
        
        report = []
        report.append("=" * 60)
        report.append("代码修复Agent日志分析报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 修复统计
        report.append("📊 修复统计")
        report.append("-" * 30)
        if "error" not in fix_summary:
            report.append(f"总处理文件数: {fix_summary['total_files']}")
            report.append(f"总修复建议数: {fix_summary['total_fixes']}")
            report.append(f"成功应用修复: {fix_summary['applied_fixes']}")
            report.append(f"修复成功率: {fix_summary['success_rate']:.2%}")
        else:
            report.append(f"错误: {fix_summary['error']}")
        report.append("")
        
        # 性能统计
        report.append("⚡ 性能统计")
        report.append("-" * 30)
        if "error" not in performance_stats:
            report.append(f"总处理时间: {performance_stats['total_processing_time']:.2f}秒")
            report.append(f"平均处理时间: {performance_stats['average_processing_time']:.2f}秒")
            report.append(f"中位数处理时间: {performance_stats['median_processing_time']:.2f}秒")
            report.append(f"平均修复速度: {performance_stats['average_fixes_per_second']:.2f} fixes/秒")
        else:
            report.append(f"错误: {performance_stats['error']}")
        report.append("")
        
        # 错误统计
        report.append("❌ 错误统计")
        report.append("-" * 30)
        if "error" not in error_summary:
            report.append(f"总错误数: {error_summary['total_errors']}")
            if error_summary['error_types']:
                report.append("错误类型分布:")
                for error_type, count in error_summary['error_types'].items():
                    report.append(f"  - {error_type}: {count}")
        else:
            report.append(f"错误: {error_summary['error']}")
        report.append("")
        
        # 每日统计
        report.append("📅 最近7天统计")
        report.append("-" * 30)
        for date, stats in sorted(daily_stats.items()):
            report.append(f"{date}: 处理{stats['files_processed']}个文件, "
                         f"应用{stats['fixes_applied']}个修复, "
                         f"{stats['errors']}个错误, "
                         f"耗时{stats['processing_time']:.1f}秒")
        
        return "\n".join(report)


def analyze_logs(log_dir: str = "logs") -> str:
    """分析日志并生成报告"""
    analyzer = LogAnalyzer(log_dir)
    return analyzer.generate_report()


if __name__ == "__main__":
    print(analyze_logs())
