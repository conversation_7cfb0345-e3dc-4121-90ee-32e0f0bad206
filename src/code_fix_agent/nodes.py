"""
代码修复Agent的节点定义
"""

import json
import os
import time
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

from .state import CodeFixState, CodeFixSuggestions
from .tools import tools
from .logger import get_logger


def create_model():
    """创建语言模型"""
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    
    if not api_key:
        raise ValueError("OPENAI_API_KEY environment variable is required")
    
    return ChatOpenAI(
        model="gpt-4o",
        temperature=0,
        api_key=api_key,
        base_url=base_url
    )


def analyze_issues(state: CodeFixState) -> Dict[str, Any]:
    """分析代码质量问题"""
    issues = state["issues"]
    file_path = state["file_path"]

    # 记录修复开始
    logger = get_logger()
    logger.log_fix_start(file_path, issues)

    # 检查是否需要使用工具获取更多信息
    needs_tools = False
    for issue in issues:
        rule = issue.get("rule", "")
        if rule and not state.get("rule_details", {}).get(rule):
            needs_tools = True
            break
    
    if needs_tools:
        # 构建工具调用消息
        system_message = SystemMessage(content="""你是一个代码质量分析专家。
        请使用提供的工具获取规则详情和源代码内容，以便进行准确的问题分析。""")
        
        issues_text = json.dumps(issues, ensure_ascii=False, indent=2)
        human_message = HumanMessage(content=f"""
        请分析以下代码质量问题：
        文件路径: {file_path}
        问题列表: {issues_text}
        
        请使用工具获取：
        1. 相关规则的详细信息
        2. 源代码文件内容
        """)
        
        return {
            "messages": [system_message, human_message],
            "next_action": "use_tools"
        }
    else:
        return {"next_action": "generate_fixes"}


def should_use_tools(state: CodeFixState) -> str:
    """判断是否需要使用工具"""
    return state.get("next_action", "continue")


def generate_fixes(state: CodeFixState) -> Dict[str, Any]:
    """生成代码修复方案"""
    issues = state["issues"]
    file_path = state["file_path"]
    messages = state.get("messages", [])
    
    logger = get_logger()
    logger.logger.info(f"🔧 生成修复方案...")

    # 记录开始时间用于性能统计
    start_time = time.time()

    # 创建输出解析器
    output_parser = PydanticOutputParser(pydantic_object=CodeFixSuggestions)
    
    # 使用PromptTemplate和OutputParser
    prompt_template = PromptTemplate(
        template="""你是一个代码修复专家。基于提供的代码质量问题、规则详情和源代码，生成具体的修复方案。

文件路径: {file_path}
Issues: {issues}

请仔细分析代码，特别注意变量的使用情况。

对于 findbugs:WMI_WRONG_MAP_ITERATOR 问题：
这个问题通常出现在使用 map.keySet() 迭代时，同时还调用了 map.get(key)。
正确的修复方法是：
1. 将 for (Object key : map.keySet()) 改为 for (Map.Entry<Object, Object> entry : map.entrySet())
2. 添加变量提取：Object key = entry.getKey();
3. 将 map.get(key) 改为 entry.getValue()
4. 修复所有使用原变量的地方

修复原则：
- 完全符合规则要求
- 保持代码逻辑正确性
- 必须确保修复后代码语法正确，能够编译通过
- 分析所有变量依赖，生成完整的修复方案

关键要求：
1. 如果修改了循环变量名，必须添加变量提取语句
2. 必须修复所有使用该变量的地方
3. 使用多个fix条目处理不同的修改
4. 每个fix必须指定正确的行号

{format_instructions}""",
        input_variables=["file_path", "issues"],
        partial_variables={"format_instructions": output_parser.get_format_instructions()}
    )
    
    # 准备输入数据
    issues_text = json.dumps(issues, ensure_ascii=False, indent=2)
    
    # 格式化提示
    formatted_prompt = prompt_template.format(
        file_path=file_path,
        issues=issues_text
    )
    
    # 构建消息
    if messages:
        full_messages = messages + [HumanMessage(content=formatted_prompt)]
    else:
        full_messages = [HumanMessage(content=formatted_prompt)]
    
    try:
        # 调用模型
        model = create_model()
        response = model.invoke(full_messages)
        
        # 使用OutputParser解析响应
        parsed_output = output_parser.parse(response.content)
        
        # 转换为原有格式以保持兼容性
        fix_suggestions = []
        for fix in parsed_output.fixes:
            fix_suggestions.append({
                "line": fix.line,
                "before": fix.before or "",
                "after": fix.after,
                "insert_after": fix.insert_after,
                "operation": fix.operation
            })
        
        # 记录修复方案生成成功
        logger.log_fix_generation(file_path, len(fix_suggestions), True)

        return {
            "fix_suggestions": fix_suggestions,
            "messages": messages + [response],
            "parsed_summary": parsed_output.summary
        }
        
    except Exception as e:
        # 记录修复方案生成失败
        logger.log_fix_generation(file_path, 0, False, str(e))
        return {
            "fix_suggestions": [],
            "messages": messages,
            "error": str(e)
        }


def apply_fixes(state: CodeFixState) -> Dict[str, Any]:
    """应用修复到文件"""
    fix_suggestions = state.get("fix_suggestions", [])
    file_path = state["file_path"]
    original_code = state.get("original_code", "")
    issues = state.get("issues", [])

    logger = get_logger()
    logger.logger.info(f"📝 应用修复到文件...")

    # 记录开始时间用于性能统计
    start_time = time.time()

    if not fix_suggestions:
        return {
            "result": {"status": "no_fixes", "message": "没有修复建议需要应用"},
            "messages": state.get("messages", [])
        }
    
    # 获取完整路径
    from config import get_full_path
    full_path = get_full_path(file_path)
    
    # 读取原始代码（如果还没有读取）
    if not original_code:
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                original_code = f.read()
        except Exception as e:
            error_msg = f"CRITICAL ERROR: 无法读取文件 {full_path}: {e}"
            logger.log_error("FILE_READ_ERROR", error_msg, file_path)
            return {"error": error_msg}
    
    # 应用修复
    lines = original_code.splitlines()
    
    # 按行号倒序排序，避免行号偏移问题
    sorted_fixes = sorted(fix_suggestions, key=lambda x: x.get("line", 0), reverse=True)
    
    applied_fixes = 0
    
    for fix in sorted_fixes:
        line_num = fix.get("line", 0)
        before = fix.get("before", "") or ""
        after = fix.get("after", "") or ""
        insert_after = fix.get("insert_after", None)
        operation = fix.get("operation", "replace")
        
        if 1 <= line_num <= len(lines):
            if operation == "insert":
                if insert_after:
                    lines.insert(line_num, insert_after)
                    print(f"✅ 在第 {line_num} 行后插入新行")
                    applied_fixes += 1
            elif operation == "delete":
                if 1 <= line_num <= len(lines):
                    del lines[line_num - 1]
                    print(f"✅ 删除第 {line_num} 行")
                    applied_fixes += 1
            else:
                # 替换行（默认操作）
                original_line = lines[line_num - 1]
                if before.strip() in original_line or original_line.strip() == before.strip():
                    lines[line_num - 1] = after
                    print(f"✅ 修复第 {line_num} 行")
                    applied_fixes += 1
                    
                    # 如果有插入内容，在替换后插入
                    if insert_after:
                        lines.insert(line_num, insert_after)
                        print(f"✅ 在第 {line_num} 行后插入额外内容")
                else:
                    # 尝试在附近行查找匹配的内容
                    found = False
                    search_range = 5
                    
                    for offset in range(-search_range, search_range + 1):
                        search_line_num = line_num + offset
                        if 1 <= search_line_num <= len(lines):
                            search_line = lines[search_line_num - 1]
                            if before.strip() in search_line or search_line.strip() == before.strip():
                                lines[search_line_num - 1] = after
                                print(f"✅ 修复第 {search_line_num} 行 (偏移 {offset})")
                                applied_fixes += 1
                                
                                if insert_after:
                                    lines.insert(search_line_num, insert_after)
                                    print(f"✅ 在第 {search_line_num} 行后插入额外内容")
                                found = True
                                break
                    
                    if not found:
                        print(f"⚠️ 警告: 无法找到第 {line_num} 行的匹配内容")
    
    # 写入修复后的文件
    try:
        modified_code = '\n'.join(lines)
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(modified_code)
        
        # 计算处理时间
        processing_time = time.time() - start_time

        # 使用新的日志记录系统
        logger.log_fix_application(file_path, applied_fixes, len(fix_suggestions), fix_suggestions)
        logger.log_performance_stats(file_path, processing_time, len(issues), applied_fixes)

        result = {
            "status": "success",
            "applied_fixes": applied_fixes,
            "total_fixes": len(fix_suggestions),
            "file_path": file_path,
            "processing_time": processing_time
        }
        
        return {
            "result": result,
            "messages": state.get("messages", [])
        }
        
    except Exception as e:
        error_msg = f"写入文件失败: {e}"
        logger.log_error("FILE_WRITE_ERROR", error_msg, file_path)
        return {"error": error_msg}
