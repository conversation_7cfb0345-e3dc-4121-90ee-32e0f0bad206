2025-06-07 10:52:37,589 - code_fix_agent - INFO - 🚀 开始修复文件: test/example.java
2025-06-07 10:52:37,589 - code_fix_agent - INFO - 📋 发现 2 个问题
2025-06-07 10:52:37,589 - code_fix_agent - DEBUG -   问题 1: findbugs:WMI_WRONG_MAP_ITERATOR (行 25, 严重性: MAJOR)
2025-06-07 10:52:37,589 - code_fix_agent - DEBUG -   问题 2: squid:S2111 (行 30, 严重性: MINOR)
2025-06-07 10:52:37,589 - code_fix_agent - DEBUG - ✅ 成功获取规则详情: findbugs:WMI_WRONG_MAP_ITERATOR
2025-06-07 10:52:37,589 - code_fix_agent - WARNING - ⚠️ 规则查询失败: unknown:RULE
2025-06-07 10:52:37,589 - code_fix_agent - WARNING -    详情: 规则不存在
2025-06-07 10:52:37,589 - code_fix_agent - INFO - ✅ 生成了 2 个修复建议 for test/example.java
2025-06-07 10:52:37,589 - code_fix_agent - INFO - 📝 修复应用完成: 2/2 for test/example.java
2025-06-07 10:52:37,590 - code_fix_agent - DEBUG -   修复 1: 行 25 (replace)
2025-06-07 10:52:37,590 - code_fix_agent - DEBUG -   修复 2: 行 26 (insert)
2025-06-07 10:52:37,590 - code_fix_agent - ERROR - ❌ TEST_ERROR: 这是一个测试错误
2025-06-07 10:52:37,590 - code_fix_agent - INFO - ⏱️ 处理时间: 1.50s, 修复速度: 1.33 fixes/s
2025-06-07 10:56:27,367 - code_fix_agent - INFO - 🚀 开始修复文件: test/example.java
2025-06-07 10:56:27,367 - code_fix_agent - INFO - 📋 发现 1 个问题
2025-06-07 10:56:27,367 - code_fix_agent - INFO - ✅ 生成了 1 个修复建议 for test/example.java
2025-06-07 10:56:27,368 - code_fix_agent - INFO - 📝 修复应用完成: 1/1 for test/example.java
2025-06-07 10:56:27,369 - code_fix_agent - INFO - ⏱️ 处理时间: 0.50s, 修复速度: 2.00 fixes/s
