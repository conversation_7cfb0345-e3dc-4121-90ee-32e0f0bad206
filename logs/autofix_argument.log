{"timestamp": "2025-06-07T10:52:37.589876", "file_path": "test/example.java", "applied_fixes": 2, "total_fixes": 2, "success_rate": 1.0, "fixes": [{"line": 25, "before": "for (Object key : map.keySet())", "after": "for (Map.Entry<Object, Object> entry : map.entrySet())", "operation": "replace"}, {"line": 26, "before": "", "after": "Object key = entry.getKey();", "operation": "insert"}]}
{"timestamp": "2025-06-07T10:56:27.368057", "file_path": "test/example.java", "applied_fixes": 1, "total_fixes": 1, "success_rate": 1.0, "fixes": [{"line": 1, "before": "old", "after": "new", "operation": "replace"}]}
