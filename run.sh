#!/bin/bash

# 代码质量Issue自动修复Agent启动脚本 (LangGraph版本)
# 支持完整的日志记录和分析功能

echo "🤖 代码质量Issue自动修复Agent (LangGraph)"
echo "========================================"
echo "版本: 1.0.0 - 支持智能日志记录和分析"
echo ""

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查LangGraph CLI
if ! command -v langgraph &> /dev/null; then
    echo "⚠️  LangGraph CLI 未安装，正在安装..."
    pip install langgraph-cli
fi

# 检查依赖
if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt 文件不存在"
    exit 1
fi

# 检查项目配置
if [ ! -f "langgraph.json" ]; then
    echo "❌ langgraph.json 配置文件不存在"
    exit 1
fi

# 检查和创建日志目录
if [ ! -d "logs" ]; then
    echo "📁 创建日志目录..."
    mkdir -p logs
fi

# 检查API密钥
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  OPENAI_API_KEY 环境变量未设置"
    echo "请设置您的OpenAI API密钥和endpoint:"
    echo "export OPENAI_API_KEY='your-api-key-here'"
    echo "export OPENAI_BASE_URL='your-endpoint-url'  # 可选"
    echo "export LOG_LEVEL='INFO'  # 可选，日志级别"
    echo ""
    read -p "是否继续运行? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 设置默认日志级别
if [ -z "$LOG_LEVEL" ]; then
    export LOG_LEVEL="INFO"
fi

echo "📊 当前配置:"
echo "  - 日志级别: $LOG_LEVEL"
echo "  - 日志目录: logs/"
if [ -n "$OPENAI_BASE_URL" ]; then
    echo "  - API端点: $OPENAI_BASE_URL"
fi

# 安装依赖
echo "📦 安装依赖包..."
pip install -r requirements.txt

# 显示菜单
echo ""
echo "请选择运行模式:"
echo "1) 启动LangGraph开发服务器 (推荐)"
echo "2) 使用命令行接口 - 处理所有文件"
echo "3) 使用命令行接口 - 处理单个文件"
echo "4) 查看日志分析报告"
echo "5) 查看项目信息"
echo "6) 清理日志文件"
echo "7) 测试日志系统"
echo "8) 退出"
echo ""

read -p "请输入选择 (1-8): " choice

case $choice in
    1)
        echo "🚀 启动LangGraph开发服务器..."
        echo "这将启动一个Web界面，你可以："
        echo "  - 可视化工作流程"
        echo "  - 交互式测试Agent"
        echo "  - 查看执行日志"
        echo "  - 调试工作流"
        echo ""
        echo "服务器启动后，请在浏览器中访问显示的URL"
        echo ""
        langgraph dev
        ;;
    2)
        echo "💻 启动命令行接口 - 处理所有文件..."
        echo "这将处理 data/issues.json 中的所有文件"
        echo ""
        python3 -m src.code_fix_agent.cli
        ;;
    3)
        echo "💻 启动命令行接口 - 处理单个文件..."
        echo "请输入要处理的文件路径（相对于项目根目录）:"
        read -p "文件路径: " file_path
        if [ -n "$file_path" ]; then
            echo "处理文件: $file_path"
            python3 -m src.code_fix_agent.cli "$file_path"
        else
            echo "❌ 文件路径不能为空"
        fi
        ;;
    4)
        echo "📊 查看日志分析报告..."
        echo ""
        python3 -m src.code_fix_agent.cli --logs
        ;;
    5)
        echo "📋 项目信息:"
        echo "  - 项目类型: LangGraph代码质量修复Agent"
        echo "  - 配置文件: langgraph.json"
        echo "  - 核心代码: src/code_fix_agent/"
        echo "  - 数据文件: data/"
        echo "  - 日志目录: logs/"
        echo ""
        echo "📁 当前文件结构:"
        find . -type f -name "*.py" -o -name "*.json" -o -name "*.md" | head -20
        echo ""
        echo "📊 日志文件状态:"
        if [ -d "logs" ]; then
            ls -la logs/ 2>/dev/null || echo "  日志目录为空"
        else
            echo "  日志目录不存在"
        fi
        ;;
    6)
        echo "🧹 清理日志文件..."
        read -p "确定要清理所有日志文件吗? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if [ -d "logs" ]; then
                # 备份当前日志
                timestamp=$(date +"%Y%m%d_%H%M%S")
                backup_dir="logs_backup_$timestamp"
                echo "📦 备份日志到: $backup_dir"
                cp -r logs "$backup_dir"

                # 清理日志文件
                rm -f logs/*.log
                echo "✅ 日志文件已清理"
                echo "📦 备份保存在: $backup_dir"
            else
                echo "📁 日志目录不存在，无需清理"
            fi
        else
            echo "❌ 取消清理操作"
        fi
        ;;
    7)
        echo "🧪 测试日志系统..."
        echo "这将运行日志系统测试，验证所有功能是否正常"
        echo ""

        # 创建临时测试脚本
        cat > test_logging_temp.py << 'EOF'
#!/usr/bin/env python3
import sys
import time
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.code_fix_agent.logger import get_logger, log_fix_start, log_fix_application, log_error, log_performance_stats
from src.code_fix_agent.log_analyzer import LogAnalyzer

def test_logging():
    print("🧪 测试日志记录功能...")
    logger = get_logger()

    test_file = "test/example.java"
    test_issues = [{"rule": "test:RULE", "line": 1, "severity": "INFO"}]
    test_fixes = [{"line": 1, "before": "old", "after": "new", "operation": "replace"}]

    log_fix_start(test_file, test_issues)
    logger.log_rule_query("test:RULE", True)
    logger.log_fix_generation(test_file, 1, True)
    log_fix_application(test_file, 1, 1, test_fixes)
    log_performance_stats(test_file, 0.5, 1, 1)

    print("✅ 日志记录测试完成!")

    time.sleep(0.1)
    analyzer = LogAnalyzer()
    report = analyzer.generate_report()
    print("\n📊 日志分析报告:")
    print(report)

if __name__ == "__main__":
    test_logging()
EOF

        python3 test_logging_temp.py
        rm -f test_logging_temp.py
        ;;
    8)
        echo "👋 再见!"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "✅ 执行完成!"

# 显示有用的信息
echo ""
echo "📚 更多信息:"
echo "  - 项目文档: PROJECT_SUMMARY.md"
echo "  - 日志系统文档: docs/LOGGING.md"
echo "  - 项目README: README.md"
echo "  - LangGraph文档: https://langchain-ai.github.io/langgraph/"
echo ""
echo "🔧 常用命令:"
echo "  - 查看日志分析: python3 -m src.code_fix_agent.cli --logs"
echo "  - 处理单个文件: python3 -m src.code_fix_agent.cli 'path/to/file.java'"
echo "  - 设置日志级别: export LOG_LEVEL=DEBUG"
echo ""
echo "📊 日志文件位置:"
if [ -d "logs" ]; then
    echo "  - 主日志: logs/agent.log"
    echo "  - 修复记录: logs/autofix_argument.log"
    echo "  - 性能统计: logs/fix_stats.log"
    echo "  - 错误记录: logs/errors.log"
else
    echo "  - 日志目录: logs/ (将在首次运行时创建)"
fi
echo ""
echo "❓ 问题反馈: 请查看项目文档或联系开发团队"
