#!/bin/bash

# 代码质量Issue自动修复Agent启动脚本 (LangGraph版本)

echo "🤖 代码质量Issue自动修复Agent (LangGraph)"
echo "========================================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查LangGraph CLI
if ! command -v langgraph &> /dev/null; then
    echo "⚠️  LangGraph CLI 未安装，正在安装..."
    pip install langgraph-cli
fi

# 检查依赖
if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt 文件不存在"
    exit 1
fi

# 检查项目配置
if [ ! -f "langgraph.json" ]; then
    echo "❌ langgraph.json 配置文件不存在"
    exit 1
fi

# 检查API密钥
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  OPENAI_API_KEY 环境变量未设置"
    echo "请设置您的OpenAI API密钥和endpoint:"
    echo "export OPENAI_API_KEY='your-api-key-here'"
    echo "export OPENAI_BASE_URL='your-endpoint-url'  # 可选"
    echo ""
    read -p "是否继续运行? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 安装依赖
echo "📦 安装依赖包..."
pip install -r requirements.txt

# 显示菜单
echo ""
echo "请选择运行模式:"
echo "1) 启动LangGraph开发服务器 (推荐)"
echo "2) 使用命令行接口"
echo "3) 查看项目信息"
echo "4) 退出"
echo ""

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🚀 启动LangGraph开发服务器..."
        echo "这将启动一个Web界面，你可以："
        echo "  - 可视化工作流程"
        echo "  - 交互式测试Agent"
        echo "  - 查看执行日志"
        echo "  - 调试工作流"
        echo ""
        echo "服务器启动后，请在浏览器中访问显示的URL"
        echo ""
        langgraph dev
        ;;
    2)
        echo "💻 启动命令行接口..."
        python3 -m src.code_fix_agent.cli
        ;;
    3)
        echo "📋 项目信息:"
        echo "  - 项目类型: LangGraph代码质量修复Agent"
        echo "  - 配置文件: langgraph.json"
        echo "  - 核心代码: src/code_fix_agent/"
        echo "  - 数据文件: data/"
        echo ""
        echo "📁 当前文件结构:"
        find . -type f -name "*.py" -o -name "*.json" -o -name "*.md" | head -20
        ;;
    4)
        echo "👋 再见!"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "✅ 执行完成!"

# 显示有用的信息
echo ""
echo "📚 更多信息:"
echo "  - 项目文档: PROJECT_SUMMARY.md"
echo "  - LangGraph文档: https://langchain-ai.github.io/langgraph/"
echo "  - 问题反馈: 请查看项目文档或联系开发团队"
