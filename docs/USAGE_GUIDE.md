# 使用指南

本指南将帮助您快速上手代码修复Agent，并充分利用新的日志记录功能。

## 🚀 快速开始

### 1. 使用启动脚本（推荐）

最简单的方式是使用 `run.sh` 启动脚本：

```bash
./run.sh
```

脚本会显示一个交互式菜单，包含以下选项：

1. **启动LangGraph开发服务器** - 提供Web界面进行可视化调试
2. **处理所有文件** - 批量处理 `data/issues.json` 中的所有文件
3. **处理单个文件** - 交互式输入文件路径进行处理
4. **查看日志分析报告** - 显示详细的修复统计和性能分析
5. **查看项目信息** - 显示项目结构和日志文件状态
6. **清理日志文件** - 安全清理日志文件（会自动备份）
7. **测试日志系统** - 验证日志功能是否正常工作
8. **退出**

### 2. 直接使用命令行

```bash
# 处理所有文件
python -m src.code_fix_agent.cli

# 处理单个文件
python -m src.code_fix_agent.cli "src/main/java/Example.java"

# 查看日志分析
python -m src.code_fix_agent.cli --logs

# 指定自定义issues文件
python -m src.code_fix_agent.cli --issues-file custom_issues.json
```

## 📊 日志功能使用

### 查看日志分析报告

```bash
# 方式1：使用启动脚本
./run.sh
# 然后选择选项 4

# 方式2：直接命令行
python -m src.code_fix_agent.cli --logs
```

### 配置日志级别

```bash
# 设置详细日志（包含调试信息）
export LOG_LEVEL=DEBUG
./run.sh

# 只显示重要信息
export LOG_LEVEL=WARNING
./run.sh

# 只显示错误
export LOG_LEVEL=ERROR
./run.sh
```

### 日志文件位置

所有日志文件都保存在 `logs/` 目录下：

- `agent.log` - 主要应用日志
- `autofix_argument.log` - 修复操作详细记录（JSON格式）
- `fix_stats.log` - 性能统计数据（JSON格式）
- `errors.log` - 错误记录（JSON格式）

## 🔧 高级功能

### 1. 测试日志系统

在使用前，建议先测试日志系统是否正常：

```bash
./run.sh
# 选择选项 7 - 测试日志系统
```

### 2. 清理日志文件

当日志文件过大时，可以安全清理：

```bash
./run.sh
# 选择选项 6 - 清理日志文件
```

系统会自动备份当前日志到 `logs_backup_YYYYMMDD_HHMMSS` 目录。

### 3. 编程方式使用日志分析

```python
from src.code_fix_agent.log_analyzer import LogAnalyzer

# 创建分析器
analyzer = LogAnalyzer()

# 获取修复统计
fix_summary = analyzer.get_fix_summary()
print(f"修复成功率: {fix_summary['success_rate']:.2%}")

# 获取性能统计
performance = analyzer.get_performance_stats()
print(f"平均处理时间: {performance['average_processing_time']:.2f}秒")

# 获取错误统计
errors = analyzer.get_error_summary()
print(f"总错误数: {errors['total_errors']}")

# 生成完整报告
report = analyzer.generate_report()
print(report)
```

## 📈 监控和优化

### 1. 定期查看日志分析

建议每次运行修复后查看日志分析：

```bash
python -m src.code_fix_agent.cli --logs
```

关注以下指标：
- **修复成功率**：应该保持在较高水平
- **平均处理时间**：监控性能变化
- **错误数量**：及时发现问题

### 2. 性能优化建议

根据日志分析结果：

- 如果处理时间过长，考虑调整模型参数
- 如果错误率较高，检查输入数据质量
- 如果内存使用过高，考虑分批处理

### 3. 错误排查

当出现错误时：

1. 查看 `logs/errors.log` 了解具体错误信息
2. 设置 `LOG_LEVEL=DEBUG` 获取详细日志
3. 检查 `logs/agent.log` 查看完整的执行过程

## 🛠️ 故障排除

### 常见问题

1. **日志目录不存在**
   ```bash
   mkdir -p logs
   ```

2. **权限问题**
   ```bash
   chmod +x run.sh
   chmod -R 755 logs/
   ```

3. **API密钥未设置**
   ```bash
   export OPENAI_API_KEY="your-api-key"
   export OPENAI_BASE_URL="your-endpoint"  # 可选
   ```

4. **依赖包问题**
   ```bash
   pip install -r requirements.txt
   ```

### 获取帮助

- 查看项目文档：`PROJECT_SUMMARY.md`
- 查看日志系统文档：`docs/LOGGING.md`
- 查看项目README：`README.md`

## 💡 最佳实践

1. **运行前测试**：使用选项7测试日志系统
2. **定期监控**：每次运行后查看日志分析
3. **及时清理**：定期清理日志文件避免占用过多空间
4. **备份重要日志**：重要的修复记录应该备份保存
5. **调试模式**：遇到问题时使用DEBUG日志级别

## 🎯 使用场景

### 开发阶段
- 使用DEBUG日志级别
- 频繁查看日志分析
- 及时修复发现的问题

### 生产环境
- 使用INFO或WARNING日志级别
- 定期查看性能统计
- 监控错误率和成功率

### 批量处理
- 使用WARNING日志级别减少输出
- 定期备份日志文件
- 监控处理进度和性能
