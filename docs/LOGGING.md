# 日志记录系统

代码修复Agent现在包含了完整的日志记录系统，用于跟踪修复过程、性能统计和错误分析。

## 日志文件结构

日志系统会在 `logs/` 目录下创建以下文件：

```
logs/
├── agent.log              # 主要的应用日志
├── autofix_argument.log   # 修复操作详细记录
├── fix_stats.log          # 性能统计数据
└── errors.log             # 错误记录
```

## 日志文件说明

### 1. agent.log
主要的应用日志，包含：
- 修复过程的详细步骤
- 调试信息
- 一般性的状态信息

### 2. autofix_argument.log
修复操作的详细记录，JSON格式，每行一个记录：
```json
{
  "timestamp": "2024-01-15T10:30:45.123456",
  "file_path": "src/main/java/Example.java",
  "applied_fixes": 3,
  "total_fixes": 4,
  "success_rate": 0.75,
  "fixes": [
    {
      "line": 25,
      "before": "for (Object key : map.keySet())",
      "after": "for (Map.Entry<Object, Object> entry : map.entrySet())",
      "operation": "replace"
    }
  ]
}
```

### 3. fix_stats.log
性能统计数据，JSON格式：
```json
{
  "timestamp": "2024-01-15T10:30:45.123456",
  "file_path": "src/main/java/Example.java",
  "processing_time_seconds": 2.34,
  "issues_count": 5,
  "fixes_applied": 3,
  "fixes_per_second": 1.28
}
```

### 4. errors.log
错误记录，JSON格式：
```json
{
  "timestamp": "2024-01-15T10:30:45.123456",
  "error_type": "FILE_READ_ERROR",
  "error_message": "无法读取文件: Permission denied",
  "file_path": "src/main/java/Example.java",
  "context": {
    "additional_info": "..."
  }
}
```

## 日志级别配置

通过环境变量 `LOG_LEVEL` 配置日志级别：

```bash
export LOG_LEVEL=DEBUG    # 显示所有日志
export LOG_LEVEL=INFO     # 显示信息级别及以上（默认）
export LOG_LEVEL=WARNING  # 显示警告级别及以上
export LOG_LEVEL=ERROR    # 只显示错误
export LOG_LEVEL=CRITICAL # 只显示严重错误
```

## 使用日志分析工具

### 命令行查看日志分析
```bash
# 查看日志分析报告
python -m src.code_fix_agent.cli --logs

# 或者直接运行分析器
python src/code_fix_agent/log_analyzer.py
```

### 编程方式使用
```python
from src.code_fix_agent.log_analyzer import LogAnalyzer

# 创建分析器
analyzer = LogAnalyzer("logs")

# 获取修复统计
fix_summary = analyzer.get_fix_summary()
print(f"修复成功率: {fix_summary['success_rate']:.2%}")

# 获取性能统计
performance = analyzer.get_performance_stats()
print(f"平均处理时间: {performance['average_processing_time']:.2f}秒")

# 获取错误统计
errors = analyzer.get_error_summary()
print(f"总错误数: {errors['total_errors']}")

# 生成完整报告
report = analyzer.generate_report()
print(report)
```

## 日志记录的内容

### 修复过程记录
- 修复开始时间和文件信息
- 规则查询成功/失败
- 修复方案生成结果
- 修复应用详情
- 性能统计数据

### 错误记录
- 文件读取/写入错误
- 规则查询错误
- 修复方案生成错误
- 网络连接错误
- 其他运行时错误

### 性能统计
- 每个文件的处理时间
- 修复速度（fixes/秒）
- 内存使用情况
- API调用统计

## 日志轮转和清理

目前日志文件会持续追加，建议定期清理：

```bash
# 备份当前日志
cp logs/agent.log logs/agent.log.backup

# 清空日志文件
> logs/agent.log
> logs/autofix_argument.log
> logs/fix_stats.log
> logs/errors.log
```

## 最佳实践

1. **定期查看日志分析**：使用 `--logs` 参数定期查看修复效果
2. **监控错误率**：关注错误日志，及时发现问题
3. **性能优化**：根据性能统计调整处理策略
4. **日志备份**：重要的修复记录应该备份保存
5. **调试模式**：遇到问题时设置 `LOG_LEVEL=DEBUG` 获取详细信息

## 故障排除

### 日志文件不存在
- 确保有写入权限
- 检查 `logs/` 目录是否存在
- 确认环境变量配置正确

### 日志内容不完整
- 检查磁盘空间
- 确认没有权限问题
- 查看是否有异常退出

### 性能问题
- 调整日志级别到 WARNING 或 ERROR
- 定期清理日志文件
- 考虑使用日志轮转
